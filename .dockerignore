# Archivos y directorios a ignorar durante el build de Docker

# Directorios de build
target/
!target/*.jar
build/

# Archivos de IDE
.idea/
.vscode/
*.iml
*.ipr
*.iws

# Archivos del sistema
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Archivos temporales
*.tmp
*.temp

# Archivos de configuración local
application-local.yml
application-dev.yml

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentación
README.md
docs/

# Archivos de Maven locales
.mvn/wrapper/maven-wrapper.jar

# Archivos de respaldo
*.bak
*.backup

# Archivos de test
src/test/

# Node modules (si los hubiera)
node_modules/

# Archivos de entorno
.env
.env.local
.env.production
