# ClasesPL - Plataforma de Clases de Español para Polacos

Una aplicación web desarrollada con Spring Boot para gestionar clases de español dirigidas a estudiantes polacos.

## 🚀 Características

- **Página de presentación pública** con información sobre las clases
- **Sistema de autenticación** con dos tipos de usuarios:
  - **Profesores** (administradores): pueden crear y gestionar cursos
  - **Alumnos** (usuarios normales): pueden ver y acceder a los cursos
- **Gestión de cursos** con título, fecha de creación y última actualización
- **Base de datos PostgreSQL** para persistencia
- **Interfaz responsive** con Bootstrap 5
- **Dockerización completa** para fácil despliegue

## 🛠️ Tecnologías Utilizadas

- **Backend**: Spring Boot 3.5.0, Java 21
- **Frontend**: <PERSON><PERSON><PERSON><PERSON><PERSON>, Bootstrap 5, Font Awesome
- **Base de datos**: PostgreSQL 15
- **Seguridad**: Spring Security
- **Contenedores**: Docker & Docker Compose
- **Build**: Maven

## 📋 Requisitos Previos

- Docker y Docker Compose instalados
- Puerto 8080 y 5432 disponibles

## 🚀 Instalación y Ejecución

### Opción 1: Con Docker (Recomendado)

1. **Clonar el repositorio**
   ```bash
   git clone <url-del-repositorio>
   cd clasesPl
   ```

2. **Ejecutar con Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Acceder a la aplicación**
   - Aplicación web: http://localhost:8080
   - Adminer (admin BD): http://localhost:8081

### Opción 2: Desarrollo Local

1. **Instalar PostgreSQL** y crear la base de datos:
   ```sql
   CREATE DATABASE clasespl;
   CREATE USER clasespl_user WITH PASSWORD 'clasespl_password';
   GRANT ALL PRIVILEGES ON DATABASE clasespl TO clasespl_user;
   ```

2. **Ejecutar la aplicación**
   ```bash
   ./mvnw spring-boot:run
   ```

## 👥 Usuarios por Defecto

### Profesor (Administrador)
- **Usuario**: `admin`
- **Contraseña**: `admin123`
- **Email**: `<EMAIL>`

### Estudiante
- **Usuario**: `estudiante1`
- **Contraseña**: `student123`
- **Email**: `<EMAIL>`

## 📱 Funcionalidades

### Para Visitantes
- Ver página de presentación
- Registrarse como nuevo usuario (por defecto como alumno)
- Iniciar sesión

### Para Profesores
- Dashboard administrativo con estadísticas
- Crear, editar y eliminar cursos
- Ver lista de estudiantes registrados
- Gestionar contenido de cursos

### Para Alumnos
- Dashboard personal
- Ver cursos disponibles
- Acceder al contenido de los cursos
- Seguimiento de progreso

## 🗂️ Estructura del Proyecto

```
src/
├── main/
│   ├── java/com/adrianheras/clasesPl/
│   │   ├── config/          # Configuración de seguridad
│   │   ├── controller/      # Controladores web
│   │   ├── model/          # Entidades JPA
│   │   ├── repository/     # Repositorios de datos
│   │   └── service/        # Lógica de negocio
│   └── resources/
│       ├── templates/      # Plantillas Thymeleaf
│       ├── static/         # Recursos estáticos
│       └── application.yml # Configuración
├── Dockerfile
├── docker-compose.yml
└── init-db.sql
```

## 🐳 Servicios Docker

- **app**: Aplicación Spring Boot (puerto 8080)
- **postgres**: Base de datos PostgreSQL (puerto 5432)
- **adminer**: Interfaz web para administrar la BD (puerto 8081)

## 🔧 Configuración

### Variables de Entorno

```yaml
# Base de datos
SPRING_DATASOURCE_URL=****************************************
SPRING_DATASOURCE_USERNAME=clasespl_user
SPRING_DATASOURCE_PASSWORD=clasespl_password

# Aplicación
SERVER_PORT=8080
SPRING_PROFILES_ACTIVE=docker
```

### Puertos

- **8080**: Aplicación web principal
- **8081**: Adminer (administración de BD)
- **5432**: PostgreSQL

## 📊 Monitoreo

La aplicación incluye Spring Boot Actuator para monitoreo:

- Health check: http://localhost:8080/actuator/health
- Info: http://localhost:8080/actuator/info

## 🔒 Seguridad

- Contraseñas encriptadas con BCrypt
- Autenticación basada en sesiones
- Autorización por roles (PROFESOR/ALUMNO)
- Protección CSRF habilitada
- Validación de formularios

## 🚀 Despliegue en Producción

1. **Modificar variables de entorno** en `docker-compose.yml`
2. **Cambiar contraseñas por defecto**
3. **Configurar HTTPS** con un proxy reverso (nginx)
4. **Configurar backups** de la base de datos
5. **Monitorear logs** en `/app/logs/`

## 🤝 Contribución

1. Fork el proyecto
2. Crear una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abrir un Pull Request

## 📝 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 📞 Contacto

- **Email**: <EMAIL>
- **Proyecto**: ClasesPL - Clases de Español para Polacos

---

¡Gracias por usar ClasesPL! 🇪🇸🇵🇱
