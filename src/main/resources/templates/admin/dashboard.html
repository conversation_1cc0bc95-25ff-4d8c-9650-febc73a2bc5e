<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Administración - ClasesPL</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .stats-card {
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .navbar-brand {
            font-weight: bold;
            color: #dc3545 !important;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>ClasesPL
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/courses">
                            <i class="fas fa-book me-1"></i>Cursos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/students">
                            <i class="fas fa-users me-1"></i>Estudiantes
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-tie me-1"></i>
                            <span th:text="${user.fullName}">Profesor</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/dashboard">Mi Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-1"></i>Cerrar Sesión
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-tachometer-alt text-primary me-2"></i>
                            Panel de Administración
                        </h1>
                        <p class="text-muted mb-0">
                            Bienvenido, <span th:text="${user.fullName}" class="fw-bold"></span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card stats-card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="text-primary mb-3">
                            <i class="fas fa-book display-4"></i>
                        </div>
                        <h3 class="h2 mb-1" th:text="${totalCourses}">0</h3>
                        <p class="text-muted mb-0">Mis Cursos</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card stats-card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="text-success mb-3">
                            <i class="fas fa-users display-4"></i>
                        </div>
                        <h3 class="h2 mb-1" th:text="${totalStudents}">0</h3>
                        <p class="text-muted mb-0">Estudiantes</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card stats-card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="text-warning mb-3">
                            <i class="fas fa-calendar display-4"></i>
                        </div>
                        <h3 class="h2 mb-1">0</h3>
                        <p class="text-muted mb-0">Clases Hoy</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card stats-card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="text-info mb-3">
                            <i class="fas fa-chart-line display-4"></i>
                        </div>
                        <h3 class="h2 mb-1">100%</h3>
                        <p class="text-muted mb-0">Satisfacción</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt text-warning me-2"></i>
                            Acciones Rápidas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <a href="/courses/create" class="btn btn-primary w-100">
                                    <i class="fas fa-plus me-2"></i>Nuevo Curso
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/courses" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-book me-2"></i>Ver Cursos
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/admin/students" class="btn btn-outline-success w-100">
                                    <i class="fas fa-users me-2"></i>Estudiantes
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="#" class="btn btn-outline-info w-100">
                                    <i class="fas fa-chart-bar me-2"></i>Reportes
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Courses -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-book text-primary me-2"></i>
                            Mis Cursos Recientes
                        </h5>
                        <a href="/courses" class="btn btn-sm btn-outline-primary">Ver Todos</a>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(recentCourses)}" class="text-center py-4">
                            <i class="fas fa-book text-muted display-4 mb-3"></i>
                            <h5 class="text-muted">No tienes cursos creados</h5>
                            <p class="text-muted">Crea tu primer curso para empezar</p>
                            <a href="/courses/create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Crear Curso
                            </a>
                        </div>
                        
                        <div th:if="${!#lists.isEmpty(recentCourses)}" class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Título</th>
                                        <th>Fecha de Creación</th>
                                        <th>Última Actualización</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="course : ${recentCourses}">
                                        <td>
                                            <i class="fas fa-book text-primary me-2"></i>
                                            <span th:text="${course.title}">Título del Curso</span>
                                        </td>
                                        <td th:text="${#temporals.format(course.createdAt, 'dd/MM/yyyy HH:mm')}">01/01/2024 10:00</td>
                                        <td>
                                            <span th:if="${course.updatedAt != null}" 
                                                  th:text="${#temporals.format(course.updatedAt, 'dd/MM/yyyy HH:mm')}">01/01/2024 10:00</span>
                                            <span th:if="${course.updatedAt == null}" class="text-muted">Sin actualizar</span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a th:href="@{/courses/view/{id}(id=${course.id})}" 
                                                   class="btn btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a th:href="@{/courses/edit/{id}(id=${course.id})}" 
                                                   class="btn btn-outline-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
