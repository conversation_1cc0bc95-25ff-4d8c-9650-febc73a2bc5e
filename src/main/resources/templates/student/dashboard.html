<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Estudiante - ClasesPL</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .course-card {
            transition: transform 0.3s ease;
        }
        .course-card:hover {
            transform: translateY(-5px);
        }
        .navbar-brand {
            font-weight: bold;
            color: #dc3545 !important;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>ClasesPL
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/student/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Mi Panel
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/courses">
                            <i class="fas fa-book me-1"></i>Cursos
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-graduate me-1"></i>
                            <span th:text="${user.fullName}">Estudiante</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/dashboard">Mi Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-1"></i>Cerrar Sesión
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-user-graduate text-success me-2"></i>
                            ¡Bienvenido a tu Panel de Estudiante!
                        </h1>
                        <p class="text-muted mb-0">
                            Hola <span th:text="${user.fullName}" class="fw-bold"></span>, 
                            aquí puedes acceder a todos tus cursos de español
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="text-primary mb-3">
                            <i class="fas fa-book display-4"></i>
                        </div>
                        <h3 class="h2 mb-1" th:text="${#lists.size(availableCourses)}">0</h3>
                        <p class="text-muted mb-0">Cursos Disponibles</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="text-success mb-3">
                            <i class="fas fa-check-circle display-4"></i>
                        </div>
                        <h3 class="h2 mb-1">0</h3>
                        <p class="text-muted mb-0">Cursos Completados</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="text-warning mb-3">
                            <i class="fas fa-star display-4"></i>
                        </div>
                        <h3 class="h2 mb-1">0</h3>
                        <p class="text-muted mb-0">Puntos Ganados</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Courses -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-book text-primary me-2"></i>
                            Cursos Disponibles
                        </h5>
                        <a href="/courses" class="btn btn-sm btn-outline-primary">Ver Todos</a>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(availableCourses)}" class="text-center py-4">
                            <i class="fas fa-book text-muted display-4 mb-3"></i>
                            <h5 class="text-muted">No hay cursos disponibles</h5>
                            <p class="text-muted">Los profesores aún no han creado cursos. ¡Vuelve pronto!</p>
                        </div>
                        
                        <div th:if="${!#lists.isEmpty(availableCourses)}" class="row">
                            <div class="col-lg-4 col-md-6 mb-3" th:each="course : ${availableCourses}" th:if="${courseStat.index < 6}">
                                <div class="card course-card border-0 shadow-sm h-100">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-book text-primary me-2"></i>
                                            <span th:text="${course.title}">Título del Curso</span>
                                        </h6>
                                        <p class="card-text small text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            Profesor: <span th:text="${course.createdBy.fullName}">Nombre</span>
                                        </p>
                                        <p class="card-text small text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <span th:text="${#temporals.format(course.createdAt, 'dd/MM/yyyy')}">01/01/2024</span>
                                        </p>
                                        <div class="mt-auto">
                                            <a th:href="@{/courses/view/{id}(id=${course.id})}" 
                                               class="btn btn-primary btn-sm w-100">
                                                <i class="fas fa-eye me-1"></i>Ver Curso
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Learning Tips -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            Consejos para Aprender Español
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                                    <div>
                                        <h6>Practica todos los días</h6>
                                        <p class="text-muted small mb-0">Dedica al menos 15-20 minutos diarios al estudio</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                                    <div>
                                        <h6>Escucha música en español</h6>
                                        <p class="text-muted small mb-0">Mejora tu pronunciación y vocabulario</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                                    <div>
                                        <h6>Ve películas con subtítulos</h6>
                                        <p class="text-muted small mb-0">Primero en polaco, luego en español</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                                    <div>
                                        <h6>Habla desde el primer día</h6>
                                        <p class="text-muted small mb-0">No tengas miedo de cometer errores</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
