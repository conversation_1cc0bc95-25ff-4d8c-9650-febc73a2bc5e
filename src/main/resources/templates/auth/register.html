<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrarse - ClasesPL</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #dc3545, #ffc107);
            min-height: 100vh;
            padding: 20px 0;
        }
        .register-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .brand-logo {
            color: #dc3545;
            font-size: 2.5rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="register-card p-5">
                    <div class="text-center mb-4">
                        <div class="brand-logo">
                            <i class="fas fa-graduation-cap me-2"></i>ClasesPL
                        </div>
                        <h2 class="h4 text-muted mt-2">Crear Cuenta</h2>
                    </div>

                    <!-- Alerts -->
                    <div th:if="${error}" class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <span th:text="${error}"></span>
                    </div>

                    <!-- Registration Form -->
                    <form th:action="@{/register}" method="post" th:object="${user}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">
                                    <i class="fas fa-user me-1"></i>Nombre *
                                </label>
                                <input type="text" class="form-control" id="firstName" th:field="*{firstName}" 
                                       placeholder="Tu nombre" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('firstName')}" th:errors="*{firstName}"></div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">
                                    <i class="fas fa-user me-1"></i>Apellido *
                                </label>
                                <input type="text" class="form-control" id="lastName" th:field="*{lastName}" 
                                       placeholder="Tu apellido" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('lastName')}" th:errors="*{lastName}"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-at me-1"></i>Nombre de Usuario *
                            </label>
                            <input type="text" class="form-control" id="username" th:field="*{username}" 
                                   placeholder="Elige un nombre de usuario" required>
                            <div class="form-text">Mínimo 3 caracteres, máximo 50</div>
                            <div class="text-danger" th:if="${#fields.hasErrors('username')}" th:errors="*{username}"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>Email *
                            </label>
                            <input type="email" class="form-control" id="email" th:field="*{email}" 
                                   placeholder="<EMAIL>" required>
                            <div class="text-danger" th:if="${#fields.hasErrors('email')}" th:errors="*{email}"></div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Contraseña *
                                </label>
                                <input type="password" class="form-control" id="password" th:field="*{password}" 
                                       placeholder="Mínimo 6 caracteres" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('password')}" th:errors="*{password}"></div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirmPassword" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Confirmar Contraseña *
                                </label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" 
                                       placeholder="Repite la contraseña" required>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    Acepto los <a href="#" class="text-decoration-none">términos y condiciones</a> *
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Crear Cuenta
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="text-muted">¿Ya tienes una cuenta?</p>
                        <a href="/login" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Iniciar Sesión
                        </a>
                    </div>

                    <div class="text-center mt-3">
                        <a href="/" class="text-muted text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i>Volver al inicio
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
