<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursos - ClasesPL</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .course-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .navbar-brand {
            font-weight: bold;
            color: #dc3545 !important;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>ClasesPL
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item" sec:authorize="hasRole('PROFESOR')">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item" sec:authorize="hasRole('ALUMNO')">
                        <a class="nav-link" href="/student/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/courses">
                            <i class="fas fa-book me-1"></i>Cursos
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <span th:text="${user.fullName}">Usuario</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/dashboard">Mi Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-1"></i>Cerrar Sesión
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0">
                                <i class="fas fa-book text-primary me-2"></i>
                                <span th:if="${user.profesor}">Mis Cursos</span>
                                <span th:if="${user.alumno}">Cursos Disponibles</span>
                            </h1>
                            <p class="text-muted mb-0">
                                <span th:if="${user.profesor}">Gestiona tus cursos de español</span>
                                <span th:if="${user.alumno}">Explora los cursos disponibles</span>
                            </p>
                        </div>
                        <div sec:authorize="hasRole('PROFESOR')">
                            <a href="/courses/create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Nuevo Curso
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <div class="container mt-3" th:if="${success or error}">
            <div class="alert alert-success alert-dismissible fade show" role="alert" th:if="${success}">
                <i class="fas fa-check-circle me-2"></i>
                <span th:text="${success}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" th:if="${error}">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span th:text="${error}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>

        <!-- Courses Grid -->
        <div class="row">
            <!-- Empty State -->
            <div th:if="${#lists.isEmpty(courses)}" class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-book text-muted display-1 mb-4"></i>
                        <h3 class="text-muted mb-3">
                            <span th:if="${user.profesor}">No tienes cursos creados</span>
                            <span th:if="${user.alumno}">No hay cursos disponibles</span>
                        </h3>
                        <p class="text-muted mb-4">
                            <span th:if="${user.profesor}">Crea tu primer curso para empezar a enseñar</span>
                            <span th:if="${user.alumno}">Los profesores aún no han creado cursos</span>
                        </p>
                        <div sec:authorize="hasRole('PROFESOR')">
                            <a href="/courses/create" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus me-2"></i>Crear Mi Primer Curso
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Courses Cards -->
            <div th:if="${!#lists.isEmpty(courses)}" class="col-lg-4 col-md-6 mb-4" th:each="course : ${courses}">
                <div class="card course-card border-0 shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-book me-2"></i>
                            <span th:text="${course.title}">Título del Curso</span>
                        </h5>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                Profesor: <span th:text="${course.createdBy.fullName}">Nombre del Profesor</span>
                            </small>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                Creado: <span th:text="${#temporals.format(course.createdAt, 'dd/MM/yyyy')}">01/01/2024</span>
                            </small>
                        </div>
                        
                        <div class="mb-3" th:if="${course.updatedAt != null}">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Actualizado: <span th:text="${#temporals.format(course.updatedAt, 'dd/MM/yyyy')}">01/01/2024</span>
                            </small>
                        </div>
                        
                        <div class="mt-auto">
                            <div class="d-flex gap-2">
                                <a th:href="@{/courses/view/{id}(id=${course.id})}" 
                                   class="btn btn-outline-primary flex-fill">
                                    <i class="fas fa-eye me-1"></i>Ver
                                </a>
                                
                                <div sec:authorize="hasRole('PROFESOR')" th:if="${course.createdBy.id == user.id}">
                                    <a th:href="@{/courses/edit/{id}(id=${course.id})}" 
                                       class="btn btn-outline-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
