<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear <PERSON>urso - <PERSON>PL</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #dc3545 !important;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>ClasesPL
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/courses">
                            <i class="fas fa-book me-1"></i>Cursos
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <form th:action="@{/logout}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-outline-light">
                                <i class="fas fa-sign-out-alt me-1"></i>Cerrar Sesión
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <!-- Header -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-plus text-primary me-2"></i>
                            Crear Nuevo Curso
                        </h1>
                        <p class="text-muted mb-0">Completa la información del curso</p>
                    </div>
                </div>

                <!-- Form Card -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <form th:action="@{/courses/create}" method="post" th:object="${course}">
                            <div class="mb-4">
                                <label for="title" class="form-label">
                                    <i class="fas fa-book me-1"></i>Título del Curso *
                                </label>
                                <input type="text" 
                                       class="form-control form-control-lg" 
                                       id="title" 
                                       th:field="*{title}" 
                                       placeholder="Ej: Español Básico para Principiantes"
                                       required>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    El título debe ser descriptivo y atractivo para los estudiantes
                                </div>
                                <div class="text-danger mt-2" th:if="${#fields.hasErrors('title')}" th:errors="*{title}"></div>
                            </div>

                            <div class="mb-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-lightbulb text-warning me-2"></i>
                                            Sugerencias para el título
                                        </h6>
                                        <ul class="mb-0 small text-muted">
                                            <li>Español Básico - Nivel A1</li>
                                            <li>Conversación en Español para Polacos</li>
                                            <li>Gramática Española Intermedia</li>
                                            <li>Español de Negocios</li>
                                            <li>Preparación DELE A2/B1</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex gap-3">
                                <button type="submit" class="btn btn-primary btn-lg flex-fill">
                                    <i class="fas fa-save me-2"></i>Crear Curso
                                </button>
                                <a href="/courses" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-times me-2"></i>Cancelar
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Info Card -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            Información Importante
                        </h6>
                        <ul class="mb-0 small text-muted">
                            <li>Una vez creado, podrás agregar más contenido al curso</li>
                            <li>Los estudiantes podrán ver el curso inmediatamente</li>
                            <li>Puedes editar el título en cualquier momento</li>
                            <li>El curso se guardará con la fecha y hora actual</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
