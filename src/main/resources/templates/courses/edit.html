<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Curso - ClasesPL</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #dc3545 !important;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>ClasesPL
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/courses">
                            <i class="fas fa-book me-1"></i>Cursos
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <form th:action="@{/logout}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-outline-light">
                                <i class="fas fa-sign-out-alt me-1"></i>Cerrar Sesión
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <!-- Header -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-edit text-warning me-2"></i>
                            Editar Curso
                        </h1>
                        <p class="text-muted mb-0">Modifica la información del curso</p>
                    </div>
                </div>

                <!-- Form Card -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <form th:action="@{/courses/edit/{id}(id=${course.id})}" method="post" th:object="${course}">
                            <div class="mb-4">
                                <label for="title" class="form-label">
                                    <i class="fas fa-book me-1"></i>Título del Curso *
                                </label>
                                <input type="text" 
                                       class="form-control form-control-lg" 
                                       id="title" 
                                       th:field="*{title}" 
                                       placeholder="Ej: Español Básico para Principiantes"
                                       required>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    El título debe ser descriptivo y atractivo para los estudiantes
                                </div>
                                <div class="text-danger mt-2" th:if="${#fields.hasErrors('title')}" th:errors="*{title}"></div>
                            </div>

                            <!-- Course Info -->
                            <div class="mb-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-info-circle text-info me-2"></i>
                                            Información del Curso
                                        </h6>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    <strong>Creado:</strong><br>
                                                    <span th:text="${#temporals.format(course.createdAt, 'dd/MM/yyyy HH:mm')}">01/01/2024 10:00</span>
                                                </small>
                                            </div>
                                            <div class="col-sm-6" th:if="${course.updatedAt != null}">
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <strong>Última actualización:</strong><br>
                                                    <span th:text="${#temporals.format(course.updatedAt, 'dd/MM/yyyy HH:mm')}">01/01/2024 10:00</span>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex gap-3">
                                <button type="submit" class="btn btn-warning btn-lg flex-fill">
                                    <i class="fas fa-save me-2"></i>Guardar Cambios
                                </button>
                                <a th:href="@{/courses/view/{id}(id=${course.id})}" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-times me-2"></i>Cancelar
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Actions Card -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-cogs text-secondary me-2"></i>
                            Otras Acciones
                        </h6>
                        <div class="d-flex gap-2">
                            <a th:href="@{/courses/view/{id}(id=${course.id})}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>Ver Curso
                            </a>
                            <button type="button" class="btn btn-outline-danger btn-sm" 
                                    data-bs-toggle="modal" data-bs-target="#deleteModal">
                                <i class="fas fa-trash me-1"></i>Eliminar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        Confirmar Eliminación
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>¿Estás seguro de que quieres eliminar el curso <strong th:text="${course.title}">Título</strong>?</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>¡Atención!</strong> Esta acción no se puede deshacer.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <form th:action="@{/courses/delete/{id}(id=${course.id})}" method="post" class="d-inline">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Eliminar Curso
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
