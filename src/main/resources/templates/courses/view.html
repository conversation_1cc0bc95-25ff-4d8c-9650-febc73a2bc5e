<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${course.title + ' - ClasesPL'}">Curso - ClasesPL</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #dc3545 !important;
        }
        .course-header {
            background: linear-gradient(135deg, #dc3545, #ffc107);
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>ClasesPL
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item" sec:authorize="hasRole('PROFESOR')">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item" sec:authorize="hasRole('ALUMNO')">
                        <a class="nav-link" href="/student/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/courses">
                            <i class="fas fa-book me-1"></i>Cursos
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <span th:text="${user.fullName}">Usuario</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/dashboard">Mi Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-1"></i>Cerrar Sesión
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Course Header -->
    <div class="course-header py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-book me-3"></i>
                        <span th:text="${course.title}">Título del Curso</span>
                    </h1>
                    <p class="lead mb-0">
                        <i class="fas fa-user me-2"></i>
                        Profesor: <span th:text="${course.createdBy.fullName}">Nombre del Profesor</span>
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div th:if="${canModify}" class="btn-group">
                        <a th:href="@{/courses/edit/{id}(id=${course.id})}" 
                           class="btn btn-warning btn-lg">
                            <i class="fas fa-edit me-2"></i>Editar
                        </a>
                        <button type="button" class="btn btn-danger btn-lg" 
                                data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash me-2"></i>Eliminar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container py-4">
        <!-- Course Info -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle text-primary me-2"></i>
                            Información del Curso
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6 mb-3">
                                <strong><i class="fas fa-calendar me-2"></i>Fecha de Creación:</strong><br>
                                <span th:text="${#temporals.format(course.createdAt, 'dd/MM/yyyy HH:mm')}">01/01/2024 10:00</span>
                            </div>
                            <div class="col-sm-6 mb-3" th:if="${course.updatedAt != null}">
                                <strong><i class="fas fa-clock me-2"></i>Última Actualización:</strong><br>
                                <span th:text="${#temporals.format(course.updatedAt, 'dd/MM/yyyy HH:mm')}">01/01/2024 10:00</span>
                            </div>
                        </div>
                        
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>¡Curso en desarrollo!</strong> 
                            Pronto se añadirán lecciones, ejercicios y material didáctico.
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar text-success me-2"></i>
                            Estadísticas
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <h3 class="h4 text-primary">0</h3>
                                <small class="text-muted">Lecciones</small>
                            </div>
                            <div class="col-6 mb-3">
                                <h3 class="h4 text-success">0</h3>
                                <small class="text-muted">Estudiantes</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Course Content (Placeholder) -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list text-primary me-2"></i>
                            Contenido del Curso
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-5">
                            <i class="fas fa-book-open text-muted display-1 mb-4"></i>
                            <h4 class="text-muted mb-3">Contenido en desarrollo</h4>
                            <p class="text-muted">
                                El profesor está preparando el material didáctico para este curso.
                                <br>¡Vuelve pronto para ver las nuevas lecciones!
                            </p>
                            <div th:if="${canModify}" class="mt-4">
                                <button class="btn btn-primary" disabled>
                                    <i class="fas fa-plus me-2"></i>Agregar Lección
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="row mt-4">
            <div class="col-12">
                <a href="/courses" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Volver a Cursos
                </a>
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" th:if="${canModify}">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        Confirmar Eliminación
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>¿Estás seguro de que quieres eliminar el curso <strong th:text="${course.title}">Título</strong>?</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>¡Atención!</strong> Esta acción no se puede deshacer.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <form th:action="@{/courses/delete/{id}(id=${course.id})}" method="post" class="d-inline">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Eliminar Curso
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
