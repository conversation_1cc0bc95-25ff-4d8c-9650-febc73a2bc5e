spring:
  application:
    name: ClasesPL
  
  datasource:
    url: *****************************************
    username: clasespl_user
    password: clasespl_password
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  thymeleaf:
    cache: false
    
server:
  port: 8080

logging:
  level:
    com.adrianheras.clasesPl: DEBUG
    org.springframework.security: DEBUG
