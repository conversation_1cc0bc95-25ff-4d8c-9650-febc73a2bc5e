package com.adrianheras.clasesPl.repository;

import com.adrianheras.clasesPl.model.Course;
import com.adrianheras.clasesPl.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CourseRepository extends JpaRepository<Course, Long> {
    
    List<Course> findByCreatedBy(User createdBy);
    
    List<Course> findByCreatedByOrderByCreatedAtDesc(User createdBy);
    
    @Query("SELECT c FROM Course c ORDER BY c.createdAt DESC")
    List<Course> findAllOrderByCreatedAtDesc();
    
    @Query("SELECT c FROM Course c WHERE LOWER(c.title) LIKE LOWER(CONCAT('%', :title, '%'))")
    List<Course> findByTitleContainingIgnoreCase(String title);
}
