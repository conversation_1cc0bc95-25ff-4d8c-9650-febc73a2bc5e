package com.adrianheras.clasesPl.config;

import com.adrianheras.clasesPl.model.Course;
import com.adrianheras.clasesPl.model.User;
import com.adrianheras.clasesPl.repository.CourseRepository;
import com.adrianheras.clasesPl.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Componente para inicializar datos por defecto en la base de datos.
 * Se ejecuta automáticamente después de que Spring Boot inicie y las tablas estén creadas.
 */
@Component
public class DataInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        logger.info("🚀 INICIANDO INICIALIZACIÓN DE DATOS - ClasesPL");
        logger.info("📅 Fecha y hora: {}", LocalDateTime.now());
        
        try {
            initializeUsers();
            initializeCourses();
            
            logger.info("🎉 INICIALIZACIÓN DE DATOS COMPLETADA EXITOSAMENTE");
            logger.info("📊 Resumen:");
            logger.info("   - Usuarios totales: {}", userRepository.count());
            logger.info("   - Cursos totales: {}", courseRepository.count());
            logger.info("========================================");
            
        } catch (Exception e) {
            logger.error("❌ ERROR durante la inicialización de datos: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Inicializa usuarios por defecto si no existen
     */
    private void initializeUsers() {
        logger.info("👥 Verificando usuarios por defecto...");

        // Crear usuario administrador/profesor
        if (!userRepository.existsByUsername("admin")) {
            logger.info("👤 Creando usuario administrador...");
            
            User admin = new User();
            admin.setUsername("admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setFirstName("Administrador");
            admin.setLastName("ClasesPL");
            admin.setRole(User.Role.PROFESOR);
            admin.setEnabled(true);
            admin.setCreatedAt(LocalDateTime.now());
            
            userRepository.save(admin);
            logger.info("✅ Usuario administrador creado: <EMAIL>");
        } else {
            logger.info("ℹ️ Usuario administrador ya existe, omitiendo creación");
        }

        // Crear usuario estudiante de ejemplo
        if (!userRepository.existsByUsername("estudiante1")) {
            logger.info("🎓 Creando usuario estudiante de ejemplo...");
            
            User student = new User();
            student.setUsername("estudiante1");
            student.setEmail("<EMAIL>");
            student.setPassword(passwordEncoder.encode("student123"));
            student.setFirstName("Jan");
            student.setLastName("Kowalski");
            student.setRole(User.Role.ALUMNO);
            student.setEnabled(true);
            student.setCreatedAt(LocalDateTime.now());
            
            userRepository.save(student);
            logger.info("✅ Usuario estudiante creado: <EMAIL>");
        } else {
            logger.info("ℹ️ Usuario estudiante ya existe, omitiendo creación");
        }
    }

    /**
     * Inicializa cursos por defecto si no existen
     */
    private void initializeCourses() {
        logger.info("📚 Verificando cursos por defecto...");

        // Buscar el usuario administrador para asignar como creador del curso
        User admin = userRepository.findByUsername("admin").orElse(null);
        
        if (admin == null) {
            logger.warn("⚠️ No se encontró usuario administrador, omitiendo creación de cursos");
            return;
        }

        // Crear curso de ejemplo
        String courseTitle = "Español Básico - Nivel A1";
        if (!courseRepository.existsByTitle(courseTitle)) {
            logger.info("📖 Creando curso de ejemplo...");
            
            Course course = new Course();
            course.setTitle(courseTitle);
            course.setCreatedBy(admin);
            course.setCreatedAt(LocalDateTime.now());
            
            courseRepository.save(course);
            logger.info("✅ Curso creado: {}", courseTitle);
        } else {
            logger.info("ℹ️ Curso '{}' ya existe, omitiendo creación", courseTitle);
        }
    }
}
