package com.adrianheras.clasesPl.controller;

import com.adrianheras.clasesPl.model.Course;
import com.adrianheras.clasesPl.model.User;
import com.adrianheras.clasesPl.service.CourseService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/courses")
public class CourseController {
    
    @Autowired
    private CourseService courseService;
    
    @GetMapping
    public String listCourses(Model model, Authentication auth) {
        User user = (User) auth.getPrincipal();
        List<Course> courses;
        
        if (user.isProfesor()) {
            courses = courseService.findByCreatedBy(user);
        } else {
            courses = courseService.findAll();
        }
        
        model.addAttribute("courses", courses);
        model.addAttribute("user", user);
        return "courses/list";
    }
    
    @GetMapping("/create")
    @PreAuthorize("hasRole('PROFESOR')")
    public String showCreateForm(Model model) {
        model.addAttribute("course", new Course());
        return "courses/create";
    }
    
    @PostMapping("/create")
    @PreAuthorize("hasRole('PROFESOR')")
    public String createCourse(@Valid @ModelAttribute("course") Course course,
                              BindingResult result,
                              Authentication auth,
                              RedirectAttributes redirectAttributes) {
        
        if (result.hasErrors()) {
            return "courses/create";
        }
        
        try {
            User user = (User) auth.getPrincipal();
            courseService.createCourse(course.getTitle(), user);
            
            redirectAttributes.addFlashAttribute("success", "Curso creado exitosamente");
            return "redirect:/courses";
            
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error al crear el curso: " + e.getMessage());
            return "redirect:/courses/create";
        }
    }
    
    @GetMapping("/view/{id}")
    public String viewCourse(@PathVariable Long id, Model model, Authentication auth) {
        Optional<Course> courseOpt = courseService.findById(id);
        if (courseOpt.isEmpty()) {
            return "redirect:/courses";
        }
        
        Course course = courseOpt.get();
        User user = (User) auth.getPrincipal();
        
        model.addAttribute("course", course);
        model.addAttribute("user", user);
        model.addAttribute("canModify", courseService.canUserModifyCourse(user, course));
        
        return "courses/view";
    }
    
    @GetMapping("/edit/{id}")
    @PreAuthorize("hasRole('PROFESOR')")
    public String showEditForm(@PathVariable Long id, Model model, Authentication auth) {
        Optional<Course> courseOpt = courseService.findById(id);
        if (courseOpt.isEmpty()) {
            return "redirect:/courses";
        }
        
        Course course = courseOpt.get();
        User user = (User) auth.getPrincipal();
        
        if (!courseService.canUserModifyCourse(user, course)) {
            return "redirect:/courses";
        }
        
        model.addAttribute("course", course);
        return "courses/edit";
    }
    
    @PostMapping("/edit/{id}")
    @PreAuthorize("hasRole('PROFESOR')")
    public String updateCourse(@PathVariable Long id,
                              @Valid @ModelAttribute("course") Course course,
                              BindingResult result,
                              Authentication auth,
                              RedirectAttributes redirectAttributes) {
        
        if (result.hasErrors()) {
            return "courses/edit";
        }
        
        try {
            User user = (User) auth.getPrincipal();
            Optional<Course> existingCourseOpt = courseService.findById(id);
            
            if (existingCourseOpt.isEmpty() || 
                !courseService.canUserModifyCourse(user, existingCourseOpt.get())) {
                redirectAttributes.addFlashAttribute("error", "No tienes permisos para editar este curso");
                return "redirect:/courses";
            }
            
            courseService.updateCourse(id, course.getTitle());
            redirectAttributes.addFlashAttribute("success", "Curso actualizado exitosamente");
            return "redirect:/courses/view/" + id;
            
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error al actualizar el curso: " + e.getMessage());
            return "redirect:/courses/edit/" + id;
        }
    }
    
    @PostMapping("/delete/{id}")
    @PreAuthorize("hasRole('PROFESOR')")
    public String deleteCourse(@PathVariable Long id,
                              Authentication auth,
                              RedirectAttributes redirectAttributes) {
        
        try {
            User user = (User) auth.getPrincipal();
            Optional<Course> courseOpt = courseService.findById(id);
            
            if (courseOpt.isEmpty() || 
                !courseService.canUserModifyCourse(user, courseOpt.get())) {
                redirectAttributes.addFlashAttribute("error", "No tienes permisos para eliminar este curso");
                return "redirect:/courses";
            }
            
            courseService.deleteById(id);
            redirectAttributes.addFlashAttribute("success", "Curso eliminado exitosamente");
            
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error al eliminar el curso: " + e.getMessage());
        }
        
        return "redirect:/courses";
    }
}
