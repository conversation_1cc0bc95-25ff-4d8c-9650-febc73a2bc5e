package com.adrianheras.clasesPl.controller;

import com.adrianheras.clasesPl.model.User;
import com.adrianheras.clasesPl.service.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Controller
public class AuthController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping("/login")
    public String login(@RequestParam(value = "error", required = false) String error,
                       @RequestParam(value = "logout", required = false) String logout,
                       Model model) {
        if (error != null) {
            model.addAttribute("error", "Usuario o contraseña incorrectos");
        }
        if (logout != null) {
            model.addAttribute("message", "Has cerrado sesión correctamente");
        }
        return "auth/login";
    }
    
    @GetMapping("/register")
    public String showRegistrationForm(Model model) {
        model.addAttribute("user", new User());
        return "auth/register";
    }
    
    @PostMapping("/register")
    public String registerUser(@Valid @ModelAttribute("user") User user,
                              BindingResult result,
                              @RequestParam("confirmPassword") String confirmPassword,
                              Model model,
                              RedirectAttributes redirectAttributes) {
        
        // Validaciones adicionales
        if (userService.existsByUsername(user.getUsername())) {
            result.rejectValue("username", "error.user", "Ya existe un usuario con ese nombre");
        }
        
        if (userService.existsByEmail(user.getEmail())) {
            result.rejectValue("email", "error.user", "Ya existe un usuario con ese email");
        }
        
        if (!user.getPassword().equals(confirmPassword)) {
            result.rejectValue("password", "error.user", "Las contraseñas no coinciden");
        }
        
        if (result.hasErrors()) {
            return "auth/register";
        }
        
        try {
            // Por defecto, los nuevos usuarios son alumnos
            user.setRole(User.Role.ALUMNO);
            userService.save(user);
            
            redirectAttributes.addFlashAttribute("success", 
                "Registro exitoso. Ya puedes iniciar sesión.");
            return "redirect:/login";
            
        } catch (Exception e) {
            model.addAttribute("error", "Error al registrar el usuario: " + e.getMessage());
            return "auth/register";
        }
    }
}
