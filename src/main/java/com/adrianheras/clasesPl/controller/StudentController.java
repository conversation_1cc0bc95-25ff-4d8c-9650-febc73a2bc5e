package com.adrianheras.clasesPl.controller;

import com.adrianheras.clasesPl.model.User;
import com.adrianheras.clasesPl.service.CourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/student")
@PreAuthorize("hasRole('ALUMNO')")
public class StudentController {
    
    @Autowired
    private CourseService courseService;
    
    @GetMapping("/dashboard")
    public String dashboard(Model model, Authentication auth) {
        User user = (User) auth.getPrincipal();
        
        model.addAttribute("user", user);
        model.addAttribute("availableCourses", courseService.findAll());
        
        return "student/dashboard";
    }
}
