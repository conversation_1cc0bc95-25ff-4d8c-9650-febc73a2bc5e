package com.adrianheras.clasesPl.controller;

import com.adrianheras.clasesPl.model.User;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class HomeController {
    
    @GetMapping("/")
    public String home() {
        return "index";
    }
    
    @GetMapping("/home")
    public String homeAlias() {
        return "index";
    }
    
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getName())) {
            User user = (User) auth.getPrincipal();
            model.addAttribute("user", user);
            
            if (user.isProfesor()) {
                return "redirect:/admin/dashboard";
            } else if (user.isAlumno()) {
                return "redirect:/student/dashboard";
            }
        }
        return "redirect:/login";
    }
}
