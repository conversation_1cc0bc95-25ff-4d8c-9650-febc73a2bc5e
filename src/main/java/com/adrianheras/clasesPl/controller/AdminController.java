package com.adrianheras.clasesPl.controller;

import com.adrianheras.clasesPl.model.User;
import com.adrianheras.clasesPl.service.CourseService;
import com.adrianheras.clasesPl.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Controller
@RequestMapping("/admin")
@PreAuthorize("hasRole('PROFESOR')")
public class AdminController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private CourseService courseService;
    
    @GetMapping("/dashboard")
    public String dashboard(Model model, Authentication auth) {
        User user = (User) auth.getPrincipal();
        
        // Estadísticas
        long totalCourses = courseService.countCoursesByUser(user);
        long totalStudents = userService.findByRole(User.Role.ALUMNO).size();
        
        model.addAttribute("user", user);
        model.addAttribute("totalCourses", totalCourses);
        model.addAttribute("totalStudents", totalStudents);
        model.addAttribute("recentCourses", courseService.findByCreatedBy(user));
        
        return "admin/dashboard";
    }
    
    @GetMapping("/students")
    public String listStudents(Model model) {
        List<User> students = userService.findByRole(User.Role.ALUMNO);
        model.addAttribute("students", students);
        return "admin/students";
    }
}
