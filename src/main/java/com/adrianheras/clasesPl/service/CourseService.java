package com.adrianheras.clasesPl.service;

import com.adrianheras.clasesPl.model.Course;
import com.adrianheras.clasesPl.model.User;
import com.adrianheras.clasesPl.repository.CourseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class CourseService {
    
    @Autowired
    private CourseRepository courseRepository;
    
    public Course save(Course course) {
        return courseRepository.save(course);
    }
    
    public Optional<Course> findById(Long id) {
        return courseRepository.findById(id);
    }
    
    public List<Course> findAll() {
        return courseRepository.findAllOrderByCreatedAtDesc();
    }
    
    public List<Course> findByCreatedBy(User user) {
        return courseRepository.findByCreatedByOrderByCreatedAtDesc(user);
    }
    
    public List<Course> searchByTitle(String title) {
        return courseRepository.findByTitleContainingIgnoreCase(title);
    }
    
    public void deleteById(Long id) {
        courseRepository.deleteById(id);
    }
    
    public Course createCourse(String title, User createdBy) {
        Course course = new Course(title, createdBy);
        return save(course);
    }
    
    public Course updateCourse(Long id, String title) {
        Optional<Course> courseOpt = findById(id);
        if (courseOpt.isPresent()) {
            Course course = courseOpt.get();
            course.setTitle(title);
            return save(course);
        }
        return null;
    }
    
    public boolean canUserModifyCourse(User user, Course course) {
        // Solo el profesor que creó el curso puede modificarlo
        return user.isProfesor() && course.getCreatedBy().getId().equals(user.getId());
    }
    
    public long countCourses() {
        return courseRepository.count();
    }
    
    public long countCoursesByUser(User user) {
        return courseRepository.findByCreatedBy(user).size();
    }
}
