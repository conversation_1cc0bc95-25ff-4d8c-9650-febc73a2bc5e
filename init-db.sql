-- Script de inicialización de la base de datos ClasesPL
-- Este script se ejecuta automáticamente cuando se crea el contenedor de PostgreSQL

-- Crear extensiones útiles
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Crear usuario administrador por defecto
-- Contraseña: admin123 (encriptada con BCrypt)
INSERT INTO users (username, email, password, first_name, last_name, role, enabled, created_at) 
VALUES (
    'profe',
    '<EMAIL>',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxIcnvtcflQedW.',
    'Administrador',
    'ClasesPL',
    'PROFESOR',
    true,
    NOW()
) ON CONFLICT (username) DO NOTHING;

-- Crear usuario estudiante de ejemplo
-- Contraseña: student123 (encriptada con BCrypt)
INSERT INTO users (username, email, password, first_name, last_name, role, enabled, created_at) 
VALUES (
    'estudiante1',
    '<EMAIL>',
    '$2a$10$8K1p/H9jd7F2JQjKQV5cUeY5YOZNy6YrjKQV5cUeY5YOZNy6YrjKQ',
    'Jan',
    'Kowalski',
    'ALUMNO',
    true,
    NOW()
) ON CONFLICT (username) DO NOTHING;

-- Crear curso de ejemplo
INSERT INTO courses (title, created_by, created_at) 
SELECT 
    'Español Básico - Nivel A1',
    u.id,
    NOW()
FROM users u 
WHERE u.username = 'admin'
AND NOT EXISTS (SELECT 1 FROM courses WHERE title = 'Español Básico - Nivel A1');

-- Comentarios útiles para el desarrollo
-- Para conectarse a la base de datos desde fuera del contenedor:
-- Host: localhost
-- Puerto: 5432
-- Base de datos: clasespl
-- Usuario: clasespl_user
-- Contraseña: clasespl_password

-- Para acceder a Adminer (interfaz web de administración):
-- URL: http://localhost:8081
-- Sistema: PostgreSQL
-- Servidor: postgres
-- Usuario: clasespl_user
-- Contraseña: clasespl_password
-- Base de datos: clasespl
