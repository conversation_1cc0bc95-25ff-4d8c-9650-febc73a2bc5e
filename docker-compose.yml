version: '3.8'

services:
  # Base de datos PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: clasespl-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: clasespl
      POSTGRES_USER: clasespl_user
      POSTGRES_PASSWORD: clasespl_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - clasespl-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U clasespl_user -d clasespl"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Aplicación Spring Boot
  app:
    build: .
    container_name: clasespl-app
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: clasespl_user
      SPRING_DATASOURCE_PASSWORD: clasespl_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      SPRING_JPA_SHOW_SQL: false
      SERVER_PORT: 8080
    ports:
      - "8080:8080"
    networks:
      - clasespl-network
    volumes:
      - app_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Adminer para administración de base de datos (opcional)
#  adminer:
#    image: adminer:latest
#    container_name: clasespl-adminer
#    restart: unless-stopped
#    depends_on:
#      - postgres
#    ports:
#      - "8081:8080"
#    networks:
#      - clasespl-network
#    environment:
#      ADMINER_DEFAULT_SERVER: postgres

# Volúmenes persistentes
volumes:
  postgres_data:
    driver: local
  app_logs:
    driver: local

# Red personalizada
networks:
  clasespl-network:
    driver: bridge
